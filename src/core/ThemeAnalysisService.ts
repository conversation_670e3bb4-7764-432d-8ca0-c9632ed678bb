import { DBOS } from '@dbos-inc/dbos-sdk';
import { PromptService } from './PromptService';
import { ConversationService } from './ConversationService';
import { LLMService } from '../services/LLMService';
import { GeminiLLMService } from '../services/GeminiLLMService';
import { logger } from '../utils/Logger';

export interface ThemeAnalysisResult {
  theme: string;
  skills: string[];
  isGeneralGreeting: boolean;
  isGeneralInquiry: boolean;
  shouldEngageCharacters: boolean;
  contextualInfo?: {
    conversationHistory?: string;
    previousTheme?: string;
    previousSkills?: string[];
  };
}

export class ThemeAnalysisService {
  private static llmService: LLMService | null = null;

  // Method to set LLM service for testing
  static setLLMService(service: LLMService): void {
    ThemeAnalysisService.llmService = service;
  }

  // Helper method to get the LLM service (injected or default)
  private static getLLMService(): LLMService {
    return ThemeAnalysisService.llmService || new GeminiLLMService();
  }

  /**
   * Analyze user input to determine conversation themes and relevant character skills
   * This replaces the script generation functionality of the old system_agent
   */
  @DBOS.workflow()
  static async analyzeConversationTheme(
    userMessage: string,
    conversationId?: number,
    previousMessages?: Array<{character: string, text: string}>
  ): Promise<ThemeAnalysisResult> {
    
    // Get theme analysis system prompt (new dedicated prompt)
    const systemPrompt = await PromptService.getSystemPrompt('theme_analysis_system');
    
    // Build context for analysis
    let analysisPrompt = userMessage;
    let contextualInfo: ThemeAnalysisResult['contextualInfo'] = {};
    
    if (conversationId) {
      // Get existing conversation context
      const conversation = await ConversationService.getConversation(conversationId);
      const messages = await ConversationService.getConversationMessages(conversationId);
      
      if (conversation) {
        contextualInfo.previousTheme = conversation.theme;
        contextualInfo.previousSkills = conversation.skills ?
          (typeof conversation.skills === 'string' ? JSON.parse(conversation.skills) : conversation.skills) : [];
      }
      
      if (messages.length > 1) {
        // Build conversation history (excluding current user message)
        contextualInfo.conversationHistory = messages
          .slice(0, -1)
          .map(msg => `${msg.character}: ${msg.text}`)
          .join('\n');
      }
    }
    
    // Include interrupted messages context if provided
    if (previousMessages && previousMessages.length > 0) {
      const interruptedContext = previousMessages
        .map(msg => `${msg.character}: ${msg.text}`)
        .join('\n');
      
      analysisPrompt = `Previous conversation context:\n${contextualInfo.conversationHistory || ''}\n\nMessages being processed when interrupted:\n${interruptedContext}\n\nUser (interrupting): ${userMessage}`;
    } else if (contextualInfo.conversationHistory) {
      analysisPrompt = `Conversation history:\n${contextualInfo.conversationHistory}\n\nCurrent theme: ${contextualInfo.previousTheme || 'none'}\nCurrent skills: ${contextualInfo.previousSkills?.join(', ') || 'none'}\n\nNew user message: ${userMessage}`;
    }

    // Generate theme analysis
    const llmResponse = await this.getLLMService().generate(systemPrompt, analysisPrompt);

    // Log the analysis result
    logger.info(`=== THEME ANALYSIS ===`);
    logger.info(`Conversation: ${conversationId || 'new'}`);
    logger.info(`Analysis Result: ${JSON.stringify(llmResponse)}`);

    // The LLM service returns a ChatResponse, but we expect JSON in the reply
    // Parse the first reply message as JSON
    if (!llmResponse || !llmResponse.reply || llmResponse.reply.length === 0) {
      throw new Error('No response from LLM for theme analysis');
    }

    let analysisData: any;
    try {
      analysisData = JSON.parse(llmResponse.reply[0].text);
    } catch (error) {
      logger.error(`Failed to parse theme analysis JSON: ${llmResponse.reply[0].text}`);
      throw new Error('Invalid JSON response from LLM for theme analysis');
    }

    const result: ThemeAnalysisResult = {
      theme: analysisData.theme || 'general conversation',
      skills: Array.isArray(analysisData.skills) ? analysisData.skills : [],
      isGeneralGreeting: analysisData.theme === 'general greeting',
      isGeneralInquiry: analysisData.theme === 'general inquiry',
      shouldEngageCharacters: this.shouldEngageCharacters(analysisData.theme, analysisData.skills),
      contextualInfo
    };
    
    return result;
  }
  
  /**
   * Determine if characters should be engaged based on theme and skills
   */
  private static shouldEngageCharacters(theme: string, skills: string[]): boolean {
    // Don't engage characters for general inquiries
    if (theme === 'general inquiry') {
      return false;
    }
    
    // Engage characters if there are identified skills or if it's not a simple greeting
    return skills.length > 0 || (theme !== 'general greeting');
  }
  
  /**
   * Re-evaluate theme and skills when user interrupts ongoing conversation
   */
  @DBOS.workflow()
  static async reEvaluateThemeOnInterruption(
    userMessage: string,
    conversationId: number,
    previousMessages: Array<{character: string, text: string}>
  ): Promise<ThemeAnalysisResult> {
    
    logger.info(`Re-evaluating theme due to user interruption in conversation ${conversationId}`);
    
    const result = await this.analyzeConversationTheme(userMessage, conversationId, previousMessages);
    
    // Update conversation metadata if theme or skills changed
    const conversation = await ConversationService.getConversation(conversationId);
    if (conversation) {
      const previousTheme = conversation.theme;
      const previousSkills = conversation.skills ?
        (typeof conversation.skills === 'string' ? JSON.parse(conversation.skills) : conversation.skills) : [];
      
      const themeChanged = result.theme !== previousTheme;
      const skillsChanged = JSON.stringify(result.skills.sort()) !== JSON.stringify(previousSkills.sort());
      
      if (themeChanged || skillsChanged) {
        logger.info(`Theme/skills changed due to interruption - Theme: ${previousTheme} -> ${result.theme}, Skills: ${previousSkills.join(',')} -> ${result.skills.join(',')}`);
        
        await ConversationService.updateConversationMetadata(
          conversationId,
          result.theme,
          result.skills
        );
      }
    }
    
    return result;
  }
}
